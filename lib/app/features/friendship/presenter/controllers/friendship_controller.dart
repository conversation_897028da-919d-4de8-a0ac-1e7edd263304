// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/domain/entities/block_player_params_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/blocked_player_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_list_item_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_store_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/game_invite_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/invitation_ticket_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/profile_temp_code_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/report_player_params_entity.dart';
import 'package:quycky/app/features/friendship/domain/request_entities/invite_request.dart';
import 'package:quycky/app/features/friendship/domain/usecases/accept_invite_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/block_player_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/check_friend_match_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/generate_friendly_invitation_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/generate_profile_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_blocked_players_list_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friend_match_history_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/invite_player_to_be_friend_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friend_profile_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friendship_invitations_received_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friendship_invitations_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friendships_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/reject_invite_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/remove_friend_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/report_player_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/validate_friendly_invitation_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/validate_profile_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/presenter/store/friendship_store.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/presenter/storage/user_storage.dart';
import 'package:quycky/core/usecase/usecase.dart';

class FriendshipController {
  final FriendshipStore _friendshipStore;
  final GetFriendshipsUseCase _getFriendshipsUseCase;
  final GetFriendshipInvitationsUseCase _getFriendshipInvitationsUseCase;
  final GetFriendshipInvitationsReceivedUseCase
      _getFriendshipInvitationsReceivedUseCase;
  final GetFriendProfileUseCase _getFriendProfileUseCase;
  final RemoveFriendUsecase _removeFriendUsecase;
  final InvitePlayerToBeFriendUseCase _invitePlayerToBeFriendUseCase;
  final UserStorage _userStorage;
  final AcceptInviteUseCase _acceptInviteUseCase;
  final RejectInviteUseCase _rejectInviteUseCase;
  final CheckFriendMatchUsecase _checkFriendMatchUsecase;
  final GetFriendMatchHistoryUsecase _getFriendMatchHistoryUsecase;
  late final UserEntity loggedUser;
  final GenerateProfileTempCodeUsecase _generateProfileTempCodeUsecase;
  final ValidateProfileTempCodeUsecase _validateProfileTempCodeUsecase;
  final GenerateFriendlyInvitationTempCodeUsecase
      _generateFriendlyInvitationTempCodeUsecase;
  final ValidateFriendlyInvitationTempCodeUsecase
      _validateFriendlyInvitationTempCodeUsecase;
  final BlockPlayerUsecase _blockPlayerUsecase;
  final ReportPlayerUsecase _reportPlayerUsecase;
  final GetBlockedPlayersListUsecase _getBlockedPlayersListUsecase;

  FriendshipController(
    this._friendshipStore,
    this._checkFriendMatchUsecase,
    this._getFriendMatchHistoryUsecase,
    this._getFriendProfileUseCase,
    this._getFriendshipsUseCase,
    this._getFriendshipInvitationsUseCase,
    this._getFriendshipInvitationsReceivedUseCase,
    this._invitePlayerToBeFriendUseCase,
    this._userStorage,
    this._rejectInviteUseCase,
    this._removeFriendUsecase,
    this._acceptInviteUseCase,
    this._generateProfileTempCodeUsecase,
    this._validateProfileTempCodeUsecase,
    this._generateFriendlyInvitationTempCodeUsecase,
    this._validateFriendlyInvitationTempCodeUsecase,
    this._blockPlayerUsecase,
    this._reportPlayerUsecase,
    this._getBlockedPlayersListUsecase,
  ) {
    getUser();
  }

  getUser() async {
    final user = await _userStorage.getUser();
    loggedUser = user ?? const UserEntity(name: '');
  }

  Future<ProfileTempCodeEntity> generateProfileTempCode() async {
    final res = await _generateProfileTempCodeUsecase(NoParams());
    return res.fold(
        (l) => throw Exception(
            'There was a problem with your conection; please try again later'),
        (r) => r);
  }

  Future<ProfileTempCodeEntity> validateProfileTempCode(String code) async {
    final res = await _validateProfileTempCodeUsecase(code);
    return res.fold((l) => throw Exception(l.message), (r) => r);
  }

  Future<InvitationTicketEntity> generateFriendlyInvitationTempCode() async {
    final res = await _generateFriendlyInvitationTempCodeUsecase(NoParams());
    return res.fold(
        (l) => throw Exception(
            'There was a problem with your conection; please try again later'),
        (r) => r);
  }

  Future<InvitationTicketEntity> validateFriendlyInvitationTempCode(
      String code) async {
    final res = await _validateFriendlyInvitationTempCodeUsecase(code);
    return res.fold(
        (l) => throw Exception('Code invalid or expired'), (r) => r);
  }

  Future<FriendMatchEntity> checkFriendMatch(
      String friendId, bool isNewFriend) async {
    FriendMatchEntity ret = FriendMatchEntity(
        commomDimensions: [], matchPercent: 0, friend: UserEntity(name: ''));
    final result = await _checkFriendMatchUsecase(
        CheckFriendMatchParamsEntity(id: friendId, isNewFriend: isNewFriend));
    result.fold((left) {}, (right) {
      ret = right;
    });
    return ret;
  }

  Future<List<FriendMatchEntity>> getFriendMatchHistory() async {
    List<FriendMatchEntity> ret = [];
    final result = await _getFriendMatchHistoryUsecase(NoParams());
    result.fold((left) {}, (right) {
      ret = right;
    });
    return ret;
  }

  void updateListItems(
      List<FriendshipEntity> friendships,
      List<FriendshipInviteEntity> invitationsReceived,
      List<FriendshipInviteEntity> invitationsSent,
      List<GameInviteEntity> gameInvites) {
    List<FriendshipListItem> friendshipListItems = [];
    List<FriendshipListItem> friendshipInviteReceivedListItems = [];
    List<FriendshipListItem> friendshipInviteSentListItems = [];
    final allowedSituations = ['active', 'pendent'];
    for (var item in invitationsReceived) {
      if (allowedSituations.contains(item.situation.toLowerCase())) {
        friendshipInviteReceivedListItems.add(FriendshipListItem(
            friendshipId: int.parse(item.id),
            type: EFriendshipListItem.inviteReceived,
            user: item.user ?? const UserEntity(name: '')));
      }
    }
    for (var item in friendships) {
      if (allowedSituations.contains(item.situation.toLowerCase())) {
        final friend =
            item.friendUser.id == loggedUser.id ? item.user : item.friendUser;
        friendshipListItems.add(FriendshipListItem(
            friendshipId: int.parse(item.id),
            type: EFriendshipListItem.friendship,
            user: friend));
      }
    }
    for (var item in invitationsSent) {
      if (allowedSituations.contains(item.situation.toLowerCase())) {
        friendshipInviteSentListItems.add(FriendshipListItem(
            friendshipId: int.parse(item.id),
            type: EFriendshipListItem.inviteSent,
            user: item.invitedUser));
      }
    }

    _friendshipStore.setData(FriendshipStoreEntity(
        friendshipListItems: friendshipListItems,
        friendshipInviteSentListItems: friendshipInviteSentListItems,
        friendshipInviteReceivedListItems: friendshipInviteReceivedListItems,
        invitesReceived: invitationsReceived,
        invites: invitationsSent,
        friendships: friendships,
        gameInvites: gameInvites));
  }

  Future<List<FriendshipEntity>> getFriendships(int id) async {
    List<FriendshipEntity> ret = [];
    final result = await _getFriendshipsUseCase(id);
    result.fold((left) => {}, (right) {
      ret = right;
    });
    return ret;
  }

  bool hasReceivedInvitesPendent() {
    for (var f in _friendshipStore.state.invitesReceived) {
      if (f.situation == 'Pendent') return true;
    }
    return false;
  }

  Future<FriendshipInviteEntity?> acceptInvite(int id) async {
    FriendshipInviteEntity? ret;
    _friendshipStore.setLoading(true);
    final result = await _acceptInviteUseCase(id);
    result.fold((left) => {}, (right) {
      ret = right;
    });
    _friendshipStore.setLoading(false);
    getAllFriendshipData();
    return ret;
  }

  Future<FriendProfileEntity?> getFriendProfile(int id) async {
    FriendProfileEntity? ret;
    _friendshipStore.setLoading(true);
    final result = await _getFriendProfileUseCase(id);
    result.fold((left) => {}, (right) {
      ret = right;
    });
    _friendshipStore.setFriendProfile(ret);
    _friendshipStore.setLoading(false);
    return ret;
  }

  Future<bool> removeFriend(int friendId) async {
    _friendshipStore.setLoading(true);
    bool ret = false;
    final result = await _removeFriendUsecase(friendId);
    result.fold((left) => {}, (right) {
      ret = right;
    });
    _friendshipStore.setLoading(false);
    return ret;
  }

  Future<FriendshipInviteEntity?> rejectInvite(int id) async {
    FriendshipInviteEntity? ret;
    _friendshipStore.setLoading(true);
    final result = await _rejectInviteUseCase(id);
    result.fold((left) => {}, (right) {
      ret = right;
    });
    _friendshipStore.setLoading(false);
    getAllFriendshipData();

    return ret;
  }

  Future<List<FriendshipInviteEntity>> getFriendshipInvitationsReceived(
      int id) async {
    List<FriendshipInviteEntity> ret = [];
    final result = await _getFriendshipInvitationsReceivedUseCase(id);
    result.fold((left) => {}, (right) {
      ret = right;
    });
    return ret;
  }

  Future<List<FriendshipInviteEntity>> getFriendshipInvitations(int id) async {
    List<FriendshipInviteEntity> ret = [];

    final result = await _getFriendshipInvitationsUseCase(id);
    result.fold((left) => {}, (right) {
      ret = right;
    });
    return ret;
  }

  Future<List<GameInviteEntity>> getGameInvites(int id) async {
    // TODO: Implement API call to get game invites
    // For now, return sample data for testing
    return [
      GameInviteEntity(
        id: "1",
        gameId: "game_123",
        roomName: "Fun Room",
        situation: "pending",
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(minutes: 1)),
        player: const UserEntity(
          id: "player_1",
          name: "John Doe",
          avatarUrl: null,
        ),
      ),
      GameInviteEntity(
        id: "2",
        gameId: "game_456",
        roomName: "Challenge Arena",
        situation: "pending",
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(minutes: 1)),
        player: const UserEntity(
          id: "player_2",
          name: "Jane Smith",
          avatarUrl: null,
        ),
      ),
    ];
  }

  getAllFriendshipData() async {
    _friendshipStore.setLoading(true);
    try {
      final user = await _userStorage.getUser();
      if (user == null) return;
      int id = int.parse(user.id);
      final friendships = await getFriendships(id);
      final invitationsReceived = await getFriendshipInvitationsReceived(id);
      final invitationsSent = await getFriendshipInvitations(id);
      final gameInvites = await getGameInvites(id);
      updateListItems(
          friendships, invitationsReceived, invitationsSent, gameInvites);
    } catch (e) {
      // Handle error silently for now
    }
    _friendshipStore.setLoading(false);
  }

  Future<bool> addFriend(String invitedUserId) async {
    bool res = false;
    final user = await _userStorage.getUser();
    if (user == null) return res;

    String userId = user.id;
    final result = await _invitePlayerToBeFriendUseCase(
        InviteRequest(userId: userId, invitedUserId: invitedUserId));
    result.fold((left) => {}, (right) {
      Analytics.instance.logEvent(name: 'sent_friend_request');
      res = true;
    });
    return res;
  }

  Future<bool> blockUser(int userIdToBlock) async {
    bool res = false;
    _friendshipStore.setLoading(true);

    try {
      final user = await _userStorage.getUser();
      if (user == null) {
        _friendshipStore.setLoading(false);
        return res;
      }

      final currentUserId = int.parse(user.id);
      final params = BlockPlayerParamsEntity(
        currentUserId: currentUserId,
        userIdToBlock: userIdToBlock,
      );

      final result = await _blockPlayerUsecase(params);
      result.fold(
        (left) => {
          // Handle error - could log or show error message
        },
        (right) {
          res = right;
          Analytics.instance.logEvent(name: 'blocked_user');
        },
      );
    } catch (e) {
      // Handle exception
    }

    _friendshipStore.setLoading(false);
    return res;
  }

  Future<bool> reportUser(int userIdToReport, ReportReason reason,
      {String? customReason}) async {
    bool res = false;
    _friendshipStore.setLoading(true);

    try {
      final user = await _userStorage.getUser();
      if (user == null) {
        _friendshipStore.setLoading(false);
        return res;
      }

      final currentUserId = int.parse(user.id);
      final params = ReportPlayerParamsEntity(
        currentUserId: currentUserId,
        reportedUserId: userIdToReport,
        reasonEnum: reason,
        customReason: customReason,
      );

      final result = await _reportPlayerUsecase(params);
      result.fold(
        (left) => {
          // Handle error - could log or show error message
        },
        (right) {
          res = right;
          Analytics.instance.logEvent(name: 'reported_user');
        },
      );
    } catch (e) {
      // Handle exception
    }

    _friendshipStore.setLoading(false);
    return res;
  }

  Future<List<BlockedPlayerEntity>> getBlockedPlayersList() async {
    List<BlockedPlayerEntity> ret = [];
    _friendshipStore.setLoading(true);

    try {
      final user = await _userStorage.getUser();
      if (user == null) {
        _friendshipStore.setLoading(false);
        return ret;
      }

      final userId = int.parse(user.id);
      final result = await _getBlockedPlayersListUsecase(userId);
      result.fold(
        (left) => {
          // Handle error - could log or show error message
        },
        (right) {
          ret = right;
        },
      );
    } catch (e) {
      // Handle exception
    }

    _friendshipStore.setLoading(false);
    return ret;
  }
}
