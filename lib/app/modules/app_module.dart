import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/friendship/data/datasources/friendship_datasource_implementation.dart';
import 'package:quycky/app/features/friendship/data/repositories/friendship_repository_implementation.dart';
import 'package:quycky/app/features/friendship/domain/usecases/accept_invite_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/block_player_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/unblock_player_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/check_friend_match_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/generate_friendly_invitation_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/generate_profile_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_blocked_players_list_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friend_match_history_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/invite_player_to_be_friend_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friend_profile_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friendship_invitations_received_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friendship_invitations_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friendships_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/reject_invite_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/remove_friend_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/report_player_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/validate_friendly_invitation_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/validate_profile_temp_code_usecase.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/friendship/presenter/pages/friend_chat.dart';
import 'package:quycky/app/features/friendship/presenter/pages/friend_match.dart';
import 'package:quycky/app/features/friendship/presenter/pages/friend_profile_page.dart';
import 'package:quycky/app/features/friendship/presenter/pages/player_blocked_list.dart';
import 'package:quycky/app/features/friendship/presenter/pages/share_qrcode_page.dart';
import 'package:quycky/app/features/friendship/presenter/store/friendship_store.dart';
import 'package:quycky/app/features/game/presenter/pages/read_game_qrcode_page.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/game/presenter/store/game_countdown_store.dart';
import 'package:quycky/app/features/home/<USER>/controllers/home_controller.dart';
import 'package:quycky/app/features/home/<USER>/pages/home_page.dart';
import 'package:quycky/app/features/home/<USER>/pages/widgets/promo_slides.dart';
import 'package:quycky/app/features/home/<USER>/storage/promotion_storage.dart';
import 'package:quycky/app/features/home/<USER>/storage/remote_config_storage.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_countdown_store.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/friend_match_history.dart';
import 'package:quycky/app/features/terms/presenter/controllers/terms_readed_store.dart';
import 'package:quycky/app/features/user/data/datasources/user_datasource_implementation.dart';
import 'package:quycky/app/features/user/data/repositories/user_repository_implementation.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/domain/usecases/delete_user_account_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/get_number_of_rounds_of_logged_player_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/get_user_by_id_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/login_apple_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/login_google_usecase.dart';
import 'package:quycky/app/features/friendship/presenter/pages/friends_list_page.dart';
import 'package:quycky/app/features/home/<USER>/controllers/question_temperature_slider_store.dart';
import 'package:quycky/app/features/start/presenter/tutorial_page.dart';
import 'package:quycky/app/features/start/presenter/welcome_page.dart';
import 'package:quycky/app/features/terms/presenter/pages/terms_page.dart';
import 'package:quycky/app/features/terms/data/datasources/terms_datasource_implementation.dart';
import 'package:quycky/app/features/terms/data/repositories/terms_repository_implementation.dart';
import 'package:quycky/app/features/terms/domain/usecases/get_current_terms.dart';
import 'package:quycky/app/features/terms/domain/usecases/get_terms_html.dart';
import 'package:quycky/app/features/terms/presenter/controllers/terms_store.dart';
import 'package:quycky/app/features/user/domain/usecases/login_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/register_user_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/update_user_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/upload_user_avatar_usecase.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/storage/user_storage.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/modules/game_module.dart';
import 'package:quycky/app/modules/guards/auth_guard.dart';
import 'package:quycky/app/modules/guards/home_guard.dart';
import 'package:quycky/app/modules/user_module.dart';
import 'package:quycky/app/services/remote_config/remote_config_service.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/core/services/push_service/push_service_fcm_implementation.dart';
import 'package:quycky/core/services/storage/storage_sharedpreferences_client_implementation.dart';
import 'package:quycky/core/services/universal_links/universal_links.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/http_client/http_client_implementation.dart';
import 'package:quycky/core/utils/use_cases/get_image_from_url_uint8list_usecase.dart';
import 'package:quycky/core/utils/use_cases/open_to_friend_match_usecase.dart';
import 'package:quycky/core/utils/use_cases/open_to_invited_game_room_usecase.dart';

class AppModule extends Module {
  @override
  final List<Bind> binds = [
    Bind.lazySingleton((i) => UserStorage(i())),
    Bind.singleton((i) => UserStore(i(), i(), i(), i(), i())),
    Bind.singleton(
        (i) => UserController(i(), i(), i(), i(), i(), i(), i(), i())),
    Bind.factory((i) => RegisterUserUseCase(i())),
    Bind.lazySingleton((i) => UserDatasourceImplementation(i())),
    Bind.lazySingleton((i) => UserRepositoryImplementation(i())),
    Bind.factory((i) => TermsStore(i(), i())),
    Bind.factory((i) => DeleteUserAccountUseCase(i())),
    Bind.factory((i) => UpdateUserUseCase(i())),
    Bind.factory((i) => GetUserByIdUseCase(i())),
    Bind.factory((i) => UploadUserAvatarUseCase(i())),
    Bind.singleton((i) => StorageSharedPreferencesClientImplementation()),
    Bind.factory((i) => FriendshipDatasourceImplementation(i())),
    Bind.factory((i) => FriendshipRepositoryImplementation(i())),
    Bind.singleton((i) => UniversalLinks(i(), i(), i())),
    Bind.factory((i) => GetImageUint8ListFromUrlUseCase()),
    Bind.factory((i) => GetFriendshipsUseCase(i())),
    Bind.factory((i) => OpenToInvitedGameRoomUseCase(i(), i())),
    Bind.factory((i) => GetFriendshipInvitationsUseCase(i())),
    Bind.factory((i) => GetFriendshipInvitationsReceivedUseCase(i())),
    Bind.factory((i) => InvitePlayerToBeFriendUseCase(i())),
    Bind.factory((i) => AcceptInviteUseCase(i())),
    Bind.factory((i) => RejectInviteUseCase(i())),
    Bind.factory((i) => RemoveFriendUsecase(i())),
    Bind.factory((i) => (GetFriendProfileUseCase(i()))),
    Bind.lazySingleton(
        (i) => PushServiceFCMImplementation(i(), i(), i(), i(), i())),
    Bind.lazySingleton((i) => HttpClientImplementation(i())),
    Bind.factory((i) => GetCurrentTermsUseCase(i())),
    Bind.factory((i) => GetTermsHtmlUseCase(i())),
    Bind.lazySingleton((i) => TermsRepositoryImplementation(i())),
    Bind.lazySingleton((i) => TermsDatasourceImplementation(i())),
    Bind.lazySingleton((i) => QuestionTemperatureSliderStore(50.0)),
    Bind.factory((i) => LoginUseCase(i())),
    Bind.lazySingleton((i) => FriendshipStore()),
    Bind.factory((i) => LoginGoogleUseCase(i())),
    Bind.factory((i) => TermsReadedStore()),
    Bind.factory((i) => LoginAppleUseCase(i())),
    Bind.lazySingleton((i) => FriendshipController(i(), i(), i(), i(), i(), i(),
        i(), i(), i(), i(), i(), i(), i(), i(), i(), i(), i(), i(), i(), i())),
    Bind.factory((i) => GameStorage(i())),
    Bind.factory((i) => PromotionCountdownStore()),
    Bind.factory((i) => GetNumberOfRoundsOfLoggedPlayerUseCase(i())),
    Bind.singleton((i) => GameCountdownStore()),
    Bind.factory((i) => RemoteConfigStorage(i())),
    Bind.lazySingleton((i) => RemoteConfigStore(i())),
    Bind.singleton((i) => RemoteConfigService(i())),
    Bind.factory((i) => PromotionStorage(i())),
    Bind.singleton((i) => PromotionStore(
          i(),
        )),
    Bind.lazySingleton((i) => HomeController(i())),
    Bind.factory((i) => CheckFriendMatchUsecase(i())),
    Bind.factory((i) => GetFriendMatchHistoryUsecase(i())),
    Bind.factory((i) => GenerateProfileTempCodeUsecase(i())),
    Bind.factory((i) => ValidateProfileTempCodeUsecase(i())),
    Bind.factory((i) => GenerateFriendlyInvitationTempCodeUsecase(i())),
    Bind.factory((i) => ValidateFriendlyInvitationTempCodeUsecase(i())),
    Bind.factory((i) => OpenToFriendMatchUsecase(i())),
    Bind.factory((i) => GetBlockedPlayersListUsecase(i())),
    Bind.factory((i) => BlockPlayerUsecase(i())),
    Bind.factory((i) => UnblockPlayerUsecase(i())),
    Bind.factory((i) => ReportPlayerUsecase(i())),
  ];

  @override
  final List<ModularRoute> routes = [
    //ChildRoute(routeConstants.initialRoute, child: (_, args) => const WelcomePage(),transition: TransitionType.downToUp),
    ChildRoute(AppRoutes.terms,
        child: (_, args) => const TermsPage(),
        transition: TransitionType.upToDown),
    // ChildRoute(AppRoutes.userRegister(),
    //     child: (_, args) => const UserRegisterPage(),
    //     transition: TransitionType.upToDown),
    // ChildRoute(AppRoutes.userProfile(),
    //     child: (_, args) => const UserProfilePage(),
    //     guards: [AuthGuard()],
    //     transition: TransitionType.upToDown),
    ChildRoute(AppRoutes.home,
        child: (_, args) => const HomePage(),
        guards: [AuthGuard(), HomeGuard()],
        transition: TransitionType.fadeIn),

    ChildRoute(AppRoutes.userFriendsList,
        child: (_, args) => const FriendsListPage(),
        guards: [AuthGuard()],
        transition: TransitionType.upToDown),

    ChildRoute(AppRoutes.profileQrCodeShare,
        child: (_, args) => const ShareQrCodePage(),
        guards: [AuthGuard()],
        transition: TransitionType.fadeIn),
    ChildRoute(AppRoutes.checkFriendMatch, child: (_, args) {
      if (args.data.runtimeType == String) {
        return FriendMatch(
          friendId: args.data,
          isNewFriend: false,
        );
      }
      final data = args.data as Map<String, dynamic>;
      final friendId = data['friendId'] as String;
      final isNewFriend =
          data.containsKey('isNewFriend') ? data['isNewFriend'] as bool : false;
      return FriendMatch(friendId: friendId, isNewFriend: isNewFriend);
    }, guards: [AuthGuard()], transition: TransitionType.upToDown),
    ChildRoute(AppRoutes.userFriendProfile(),
        child: (_, args) => FriendProfilePage(id: args.params["id"]),
        guards: [AuthGuard()],
        transition: TransitionType.upToDown),
    ChildRoute(AppRoutes.initialRoute,
        child: (_, args) => const WelcomePage(),
        transition: TransitionType.upToDown),
    ChildRoute(AppRoutes.tutorial,
        child: (_, args) => TutorialPage(toHome: args.data ?? false),
        transition: TransitionType.upToDown),
    ChildRoute(AppRoutes.friendChat,
        child: (_, args) => FriendChat(
              friend: args.data['friend'] as UserEntity,
            ),
        transition: TransitionType.upToDown),
    ChildRoute(AppRoutes.userBlockedPlayersList,
        child: (_, args) => PlayerBlockedList(),
        transition: TransitionType.upToDown),
    // ModuleRoute(
    //   AppRoutes.home,
    //   module: HomeModule(),
    //   guards: [AuthGuard()],
    // ),
    ModuleRoute(
      AppRoutes.game,
      module: GameModule(),
      guards: [AuthGuard()],
    ),
    ModuleRoute(
      AppRoutes.user,
      module: UserModule(),
      // guards: [AuthGuard()],
    ),
  ];
}
